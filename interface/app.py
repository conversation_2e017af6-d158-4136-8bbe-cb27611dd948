import gradio as gr
from langchain_community.chat_models.perplexity import ChatPerplexity
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.prompts import PromptTemplate
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.documents import Document
import keywords as kw
import random
import os

# This is the main section of the app
# It creates the UI and handles the interaction with the model
# It uses the gradio library to create the UI and handle the interaction with the model
# It uses the langchain library to handle the interaction with the model
# It uses the keywords library to extract keywords from the user message and the model response

#api keys for the models
PPLX_API_KEY = os.getenv("PPLX_API_KEY")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

''' 
the ChatPerplexity model is not working (SSL certification problem), but the ChatGoogleGenerativeAI model is working fine
you can switch between the two by commenting out one of the models and uncommenting the other
you can add other models as well, but make sure to import them at the top of the file
'''
#chatn = ChatPerplexity(temperature=0.5, model="sonar", streaming=True, api_key=PPLX_API_KEY)

llm = ChatGoogleGenerativeAI(temperature=0.5, model="gemini-2.0-flash", api_key=GOOGLE_API_KEY)

# The prompt is the template that is used to generate the input for the model
# It is used to provide context to the model and guide its responses
# The context is the conversation history, which is used to provide context to the model
#The conversation history is a list of keywords that are extracted from the user message and the model response
# The input is the last user message, which is used to generate the model response
_prompt = "You are a helpful sales assistant at Graphisoft. " \
                    "You can only speak about Graphisoft products and services. " \
                    "You are not allowed to speak about anything else." \
                    "So far, the conversation has progressed along the following keywords: " \
                    "{context}" \
                    "The user has asked the following question: {input}" \
                    "Please answer the question in a friendly and helpful manner."
# We create a prompt template using the _prompt string
prompt = PromptTemplate(input_variables=["context", "input"], template=_prompt)         

# We create a chain using the llm and the prompt template
# The chain is used to generate the model response based on the user message and the conversation history
chain = create_stuff_documents_chain(llm, prompt)

# The docs variable is used to store the conversation history
# It is a list of Document objects, which are used to store the keywords extracted from the user message and the model response
docs = []

with gr.Blocks() as demo: 

    def cart_information():
        # The backend logic should return string
        backend_cart_info = random.choice(["yes", "no"]) # Simulating a backend call
        return backend_cart_info

    with gr.Row():
        #here are the window, where you can see the chat history and the input box
        chatbot = gr.Chatbot(type="messages", scale=8)
        with gr.Column():
         # output box, for the cart information
            label = gr.Label(scale=1, value="Cart information:", container=False)
            cart_box = gr.Markdown(label="Cart", container=True, min_height=10, value="Cart is empty")
            

    msg = gr.Textbox(show_label= False, submit_btn=True)

    #after the user sends a message, it will be added to the chat history immediately
    #this is done by the user function, which takes the user message and the chat history as input
    #it returns the updated chat history and clears the input box
    def user(user_message, history: list):
        #this is the user message, which is added to the chat history
        history.append({"role": "user", "content": user_message})
        return "", history

    #this function is called after the user sends a message
    #it takes the chat history as input and returns the updated chat history
    #it uses the model to generate a response to the user message
    #streaming is enabled, so the response is generated in chunks
    def bot(history: list):
        
        response = chain.invoke({"context": docs, "input": history[-1]["content"]})
        partial_message = {"role": "assistant", "content": response}
        
        summarized_message = kw.keyword_extraction(history[-1]["content"])
        docs.append(Document(page_content=summarized_message))

        summarized_message = kw.keyword_extraction(response)
        docs.append(Document(page_content=summarized_message))
        return history + [partial_message]

    

    #the message sender function
    msg.submit(user, [msg, chatbot], [msg, chatbot], queue=False).then(
        bot, chatbot, chatbot
    ).then(
       cart_information, outputs=cart_box 
    )
    


demo.launch()

