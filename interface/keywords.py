from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate
from langchain_mistralai import ChatMistralAI
from langchain_google_genai import ChatGoogleGenerativeAI
from keybert.llm import <PERSON><PERSON><PERSON><PERSON>
from keybert import KeyLLM, KeyBERT
import os

'''
Simple keyword extraction using KeyBERT and LangChain
The code uses KeyBERT to extract keywords from a given text using a language model.
The language model is provided by LangChain, which allows for easy integration with various LLMs.
The Mistral AI not working (SSL certification problem), but the Google Generative AI model is working fine.
You can switch between the two by commenting out one of the models and uncommenting the other.
'''

#You can use any LLM that is supported by LangChain, such as Mistral or Google Generative AI.
MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

#model = ChatMistralAI( model="mistral-large-latest", temperature=0,    api_key=MISTRAL_API_KEY,)
model = ChatGoogleGenerativeAI(temperature=0.5, model="gemini-2.0-flash", api_key=GOOGLE_API_KEY)


# Define the prompt for the language model
_prompt ="""
        I have a chat history and I want to extract keywords from it.
        The format of the keywords should be a comma-separated list.
        Just like this: "keyword1, keyword2, keyword3"
        This is the chat history:
        {context}
        """

# Create a prompt template using the defined prompt
prompt = ChatPromptTemplate.from_messages([("system", _prompt)])

# Create a chain for processing documents using the language model and the prompt
chain = create_stuff_documents_chain(model, prompt)

llm = LangChain(model)
# Use KeyLLM and KeyBERT to extract keywords
kw_model = KeyBERT(llm=llm)

def keyword_extraction(message):
        # Extract keywords from the documents using KeyBERT
        # The output is a list
        keywords = kw_model.extract_keywords(message, threshold=.5)

        keywords_str = ""
        # Convert the list of tuples to a string
        for keyword in keywords:
            for k in keyword:
                keywords_str += str(k) + ", "

        if not keywords_str:
            keywords_str = message
        
        # Return the keywords as a string
        return keywords_str