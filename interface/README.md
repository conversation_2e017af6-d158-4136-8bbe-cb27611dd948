# Sales Agent Interface

It uses **Gradio** for the user interface and integrates with **<PERSON><PERSON><PERSON><PERSON>** and **KeyBERT** for language model interactions and keyword extraction.

## Project Structure

### File Descriptions

- **`app.py`**:  
  This file contains the main Gradio-based chatbot interface. It uses the `ChatGoogleGenerativeAI` model for generating responses and integrates with the `keywords.py` module for keyword extraction.

- **`keywords.py`**:  
  Implements keyword extraction using KeyBERT and LangChain. It supports multiple language models, including Mistral and Google Generative AI.

- **`requirements.txt`**:  
  Lists the Python dependencies required for the project, including Gradio, LangChain, and KeyBERT.


## Features

1. **Chatbot Interface**:
   - Built using Gradio.
   - Provides a chat interface for users to interact with the sales assistant.
   - Displays cart information alongside the chat.

2. **Keyword Extraction**:
   - Extracts keywords from user messages using KeyBERT.
   - Supports integration with multiple language models via LangChain.

3. **Language Models**:
   - Uses `ChatGoogleGenerativeAI` for generating responses.
   - Includes commented-out support for `ChatPerplexity` and `ChatMistralAI`.

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd sales-agent/interface

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   
### Important
After installation, it is possible that the _langchain.py file of Keybert need to be replaced with [this](https://github.com/MaartenGr/KeyBERT/blob/master/keybert/llm/_langchain.py). 
(The bug has been fixed, but it hasn't been officially released in the 0.9.0 version)

3. Set up API keys:
   ```bash
      PLPX_API_KEY=<your-perplexity-api-key>
      GOOGLE_API_KEY=<your-google-api-key>
      MISTRAL_API_KEY=<your-mistral-api-key>

## Run
After completing the installation and setting up the dependencies, you can run the application using the following command:
  ```bash
    python app.py
  ```
## Tasks
- Not yet filtering keywords from ai messages
- Just prints the keywords to the console
- Cart information is not real, just randomly choose from these two words: yes; no
