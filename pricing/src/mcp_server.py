# mcp_server.py
import os
from mcp.server.fastmcp import FastMC<PERSON>, Context
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from pricing_repository import ProductRepository
from pricing_calculator import PricingCalculator
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Create an MCP server
mcp = FastMCP("Pricing Service")

# Database configuration - should be moved to config file in production
DB_CONFIG = {
    "dbname": os.getenv("POSTGRES_DB", "your_db_name"),
    "user": os.getenv("POSTGRES_USER", "your_user"),
    "password": os.getenv("POSTGRES_PASSWORD", "your_password"),
    "host": os.getenv("POSTGRES_HOST", "localhost"),
    "port": os.getenv("POSTGRES_PORT", "5432")
}

@dataclass
class AppContext:
    pricing_calculator: PricingCalculator
    repository: ProductRepository

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with database connections"""
    repository = ProductRepository(DB_CONFIG)
    pricing_calculator = PricingCalculator(repository)
    try:
        yield AppContext(pricing_calculator=pricing_calculator, repository=repository)
    finally:
        repository.close()

# Pass lifespan to server
mcp = FastMCP("Pricing Service", lifespan=app_lifespan)

# Tool to calculate product pricing
@mcp.tool()
def calculate_price(product_name: str, country: str, subscription: str, num_users: int, ctx: Context) -> str:
    """
    Calculate the total price for a product subscription
    
    Args:
        product_name: Name of the product
        country: Country code (e.g., US, UK)
        subscription: Subscription type (monthly, yearly, or 3 years)
        num_users: Number of users
    
    Returns:
        Pricing information including total price
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    
    try:
        result = pricing_calculator.get_total_price(
            product_name=product_name,
            country=country,
            subscription=subscription,
            num_users=num_users
        )
        return str(result)
    except ValueError as e:
        return f"Error: {str(e)}"

# Tool to list available subscription options
@mcp.tool()
def list_subscription_options(ctx: Context) -> str:
    """List all available subscription types
    Args:
        ctx: Context object containing request information
    Returns:
        str: List of available subscription options
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    options = pricing_calculator.list_subscription_options()
    return str(options)

# Tool to list available countries
@mcp.tool()
def list_countries(ctx: Context) -> str:
    """List all countries where pricing is available
    Args:
        ctx: Context object containing request information
    Returns:
        str: List of available countries
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    countries = pricing_calculator.list_countries()
    return str(countries)

# Resource for pricing by country
@mcp.tool()
def get_country_pricing(country: str, ctx: Context) -> str:
    """Get pricing options for a specific country
    Args:
        country: Country code (e.g., US, UK)
        ctx: Context object containing request information
    Returns:
        str: Pricing information for the specified country
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    pricing = pricing_calculator.get_pricing_by_country(country)
    return str(pricing)

@mcp.tool()
def get_products(ctx: Context, country = None) -> str:
    """Get all products available in a specific country or all products if no country is specified
    Args:
        ctx: Context object containing request information
        country: Country code (e.g., US, UK)(optional)
    Returns:
        str: List of all products
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    if country is None:
        products = pricing_calculator.repository.get_all_products()
    else:  # Default to the first available country
        products = pricing_calculator.repository.get_all_awailable_products(country)
    return str(products)

if __name__ == "__main__":
    mcp.run()