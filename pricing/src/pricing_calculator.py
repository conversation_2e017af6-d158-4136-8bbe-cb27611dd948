# pricing_calculator.py
from typing import Dict, Any, List
from pricing_repository import ProductRepository

class PricingCalculator:
    def __init__(self, repository: ProductRepository):
        self.repository = repository
    
    def get_total_price(self, product_name: str, country: str, subscription: str, num_users: int) -> Dict[str, Any]:
        """Calculate total price for a product subscription"""
        # Get product ID
        product_id = self.repository.get_product_id(product_name)
        if not product_id:
            raise ValueError(f"Product '{product_name}' not found.")
        
        # Get pricing details
        pricing = self.repository.get_pricing(product_id, country, subscription)
        if not pricing:
            raise ValueError(f"Pricing not found for product '{product_name}', country '{country}', and subscription '{subscription}'.")
        
        price_per_month, currency = pricing
        
        # Calculate based on subscription type
        subscription_months = {
            "monthly": 1,
            "yearly": 12,
            "3 years": 36
        }.get(subscription)
        
        if subscription_months is None:
            raise ValueError(f"Invalid subscription type '{subscription}'.")
        
        total_price = price_per_month * subscription_months * num_users
        
        return {
            "product": product_name,
            "subscription": subscription,
            "country": country,
            "currency": currency,
            "price_per_month_per_user": price_per_month,
            "users": num_users,
            "total_months": subscription_months,
            "total_price": total_price
        }
    
    def list_subscription_options(self) -> List[str]:
        """Get all available subscription types"""
        return ["monthly", "yearly", "3 years"]
    
    def list_countries(self) -> List[str]:
        """Get all countries where pricing is available"""
        return self.repository.get_all_countries()
    
    def get_pricing_by_country(self, country: str) -> List[Dict[str, Any]]:
        """Get pricing options for a specific country"""
        return self.repository.get_country_pricing(country)