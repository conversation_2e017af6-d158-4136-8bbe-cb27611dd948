-- Drop in reverse dependency order
DROP TABLE IF EXISTS public.pricing;
DROP TABLE IF EXISTS public.products;
DROP TYPE IF EXISTS public.subscription_type;

-- Recreate enum type
CREATE TYPE public.subscription_type AS ENUM ('monthly', 'yearly', '3 years');

-- Recreate products table
CREATE TABLE public.products (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT,
    logo_url TEXT
);

-- Recreate pricing table
CREATE TABLE public.pricing (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES public.products(id),
    subscription public.subscription_type,
    price_per_month NUMERIC,
    country TEXT DEFAULT 'US',
    currency CHAR(3) DEFAULT 'USD'
);
