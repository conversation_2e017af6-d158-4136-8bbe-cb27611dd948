# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environments
venv/
env/
ENV/
.env
.venv/
env_bak/
venv_bak/

# IDE specific files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# macOS specific files
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Database
*.db
*.sqlite3
*.sqlite

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment configuration
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.ini

# MCP specific files
.mcp-cache/