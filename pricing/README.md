# Pricing Service Setup Guide

## Initial Setup

### 1. Create and Activate Virtual Environment

```bash
# Create a new Python virtual environment
python3 -m venv venv

# Activate the virtual environment
source venv/bin/activate
# OR if you are on windows
.venv\Scripts\Activate.ps1
```

### 2. Install Dependencies

```bash
# Install all required packages
pip install -r requirements.txt
```

## Database Setup

### 1. Create PostgreSQL Database with Docker Compose

Create a `docker-compose.yml` file with the following content:

Start the PostgreSQL database:

```bash
# Start PostgreSQL database
docker-compose up -d
```

### 2. Create Environment Variables

Create a `.env` file in your project root with the following content:

```
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=your_database_name
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password
```

### 3. Create Database Tables

Use the provided SQL script to create necessary database tables with PostgreSQL:

```bash
# Create database tables
psql -U your_username -d your_database -f create_tables.sql
```

> **Note:** This script can also be used to reset the database when needed.

## Data Population

### 4. Add Pricing Data

You have two options for populating the database with pricing data:

#### Option A: Scrape Live Pricing Data

Follow the instructions in the `scraping` folder's README to collect real pricing data:

```bash
cd scraping
# Follow specific instructions from the scraping README
```

#### Option B: Use Example Data

Load example pricing data directly into the PostgreSQL database:

```bash
# Load example data
psql -U your_username -d your_database -f example_pricing_data.sql
```

## Running the MCP Server

### 5. Start the MCP Server

```bash
# Make sure your virtual environment is activated
source venv/bin/activate

# Run the MCP server
python src/mcp_server.py
```

### 6. Testing the MCP Server

You can test the MCP server functionality using the following methods:

#### Testing with MCP Inspector Tool

The MCP Inspector tool allows you to directly test your MCP server functionality:

```bash

# Run the inspector on your MCP server
mcp dev src/mcp_server.py
```

This will open a web interface where you can send test requests to your MCP server and view the responses.

change the command param to 
```bash 
./venv/bin/python 
``` 
or on windows to:
```bash
venv\Scripts\python 
``` 
and change the arguments param to 
```bash 
src/mcp_server.py
```
then you can connect to the server and test out the tools from the UI

#### Testing with Claude

1. Open Claude's settings
2. Navigate to Developer > MCP Settings
3. Configure your MCP server with the following settings:
   ```json
   {
       "mcpServers": {
           "Pricing Service": {
               "command": "/path/to/your/venv/bin/python",
               "args": [
                   "/path/to/your/project/src/mcp_server.py"
               ]
           }
       }
   }
   ```
4. Save the configuration and try asking Claude questions that require pricing data