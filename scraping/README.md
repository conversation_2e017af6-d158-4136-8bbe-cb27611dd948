# Graphibot

This project is designed to scrape Graphisoft's publicly available websites to be used as context about products and pricing for our sales agent proof of concept. The project uses the scrapy framework. Scraped data is stored in MongoDB for further processing and analysis.

> The project has some issues that are currently not a priority for gathering data. [See here](#current-issues)

## Getting started
To get started with this project, ensure you have Python installed. Clone the repository and install the required dependencies.

1. <PERSON>lone the repo and start working
    ```bash
    git clone https://github.com/Nemetschek-SE/sales-agent.git
    cd sales-agent/scraping
    ```

2. Best to use venv for installing project-specific dependencies
    ```bash
    python -m venv .venv #.venv name is only a suggestion
    ```

3. Activate the virtual environment
    ```bash
    source .venv/bin/activate # On Unix-based systems
    .venv\Scripts\Activate.ps1 # On Windows
    ```

4. And install the project's dependencies
    ```bash
    pip install -r requirements.txt
    ```

## Running a scrape and save responses to mongo
To run a scrape using the spiders in this project, follow these steps!

1. Ensure the virtual environment is activated
    ```bash
    source .venv/bin/activate  # On Unix-based systems
    .venv\Scripts\Activate.ps1  # On Windows
    ```

Easiest would be running mongo in a docker container. To help, I created a docker-compose file.

2. Set a password for your mongodb server
    ```yml
    services:
    mongo:
        ...
        environment:
        - MONGO_INITDB_ROOT_USERNAME=root
        - MONGO_INITDB_ROOT_PASSWORD=yourpass
        ...

    volumes:
    mongo_data:
    ```

3. Start mongo container with docker-compose
    ```bash
    docker-compose up -d
    ```

4. Create a `.env` file in `scraping/` and include the connection string for your server and include a name for the db to save the scraped data to
    ```env
    MONGODB_URI=***************************************/
    MONGODB_DATABASE=graphibot
    ```

5. Configure scrapy settings as you wish in `graphibot/settings.py`!
    - Limit the number of pages to crawl by configuring `CLOSESPIDER_ITEMCOUNT` in `settings.py`
    - Enable or disable middlewares by modifying the `DOWNLOADER_MIDDLEWARES` setting in `settings.py`
    - Enable or disable pipelines by modifying the `ITEM_PIPELINES` setting in each spiders `custom_settings`

6. Run a spider with the scrapy command-line tool
    ```bash
    scrapy crawl graphibot
    ```
    > Replace `graphibot` with the name of the spider you want to run.  
    > Refer to the `spiders/` directory for available spiders, their names and their configurations.
    > Currently, only `graphibot` and `pricingbot` is available.

## Project Components
If you are curious about scrapy's architecture, take a look at [this page](https://docs.scrapy.org/en/latest/topics/architecture.html).

### Spiders
Spiders are the core of the Scrapy project. They define how data is scraped from websites. Current spiders include:
- `graphibot`: 
    - crawls starting from [graphisoft.com](https://graphisoft.com)
    - only starts http or https requests to urls found in links on the page
    - saves responses
- `pricingbot`: 
    - handles scraping pricing data of products in all available regions

Refer to the `spiders/` directory for implementation details.

### Middlewares
The following middlewares are available in this project
- **Downloader Middlewares**: Used for handling requests and responses.
    - IgnoreSubdomainDownloadMiddleware
    - IgnoreCountryCodeDownloadMiddleware
    - IgnoreNonHtmlDownloadMiddleware
- **Spider Middlewares**: Used for processing items and requests in the spider.
    - None

Refer to `middlewares.py` for more details or to add custom middlewares.

### Pipelines
The project includes pipelines for processing scraped data. These pipelines are defined in `pipelines.py`. Examples include:
- MongoDBPipeline
- PostgreSQLPipeline
- JsonWriterPipeline
    - **⚠️ Note:** _Files with names that do not comply with OS file naming conventions are not saved. This pipeline should be avoided._

## Autothrottling Settings
Autothrottling is enabled to optimize scraping performance and reduce server load. Current settings can be found in `settings.py`. Adjust these settings as needed to balance speed and server impact.

## Current Issues
Graphibot:
- Does not filter the correct format of regions and languages
    - Possible solution might be cookiejars, like in pricingbot
- Does not handle dynamic HTTP errors (e.g. 200 OK is sent, but 404 Not Found displayed on the site)
- Does not handle dynamically rendered elements
- Does not handle sites that need authentication

Pricingbot: no known issues