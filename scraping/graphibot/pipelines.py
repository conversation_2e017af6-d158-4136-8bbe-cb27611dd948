# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

from itemadapter import ItemAdapter
from datetime import datetime, timezone
from pathlib import Path
from urllib.parse import urlparse
import pymongo
import json
import logging
import re
from datetime import datetime, timezone
from typing import Dict, Any
from psycopg2 import sql
import psycopg2

class MongoDBPipeline:
    def __init__(self, mongo_uri, mongo_db):
        self.mongo_uri = mongo_uri
        self.mongo_db = mongo_db
        self.collection_name = None

    @classmethod
    def from_crawler(cls, crawler):
        mongo_uri = crawler.settings.get("MONGODB_URI")
        mongo_db = crawler.settings.get("MONGODB_DATABASE")

        if not mongo_uri or not mongo_db:
            logging.error("MongoDB URI or Database is not set. MongoDBPipeline will not be used.")
            raise ValueError("MongoDB URI or Database is not set.")

        return cls(mongo_uri, mongo_db)

    def open_spider(self, spider):
        try:
            self.client = pymongo.MongoClient(self.mongo_uri)
            self.db = self.client[self.mongo_db]
        except Exception as e:
            logging.error(f"Failed to connect to MongoDB: {e}")
            raise

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")  # Date: YYYYMMDD_HHMMSS
        self.collection_name = f"{spider.name}_{timestamp}"  # Result: "graphibot_20240401_153000"

    def close_spider(self, spider):
        if self.client:
            self.client.close()
            logging.info(f"MongoDB connection closed for {spider.name}.")

    def process_item(self, item, spider):
        adapter = ItemAdapter(item)

        if "headers" in adapter:
            adapter["headers"] = {k.decode(): v[0].decode() for k, v in adapter["headers"].items()}

        self.db[self.collection_name].insert_one(adapter.asdict())
        return item

# get_safe_filename does not create a safe filename on all OSes
# this pipeline was just for testing purposes
class JsonWriterPipeline:
    def __init__(self):
        self.dump_dir = Path("dump")
        if self.dump_dir.exists():
            for file in self.dump_dir.iterdir():
                file.unlink()
        self.dump_dir.mkdir(exist_ok=True)

    def process_item(self, item, spider):
        filename = self.get_safe_filename(item["url"]) + ".json"

        with open(self.dump_dir / filename, "w", encoding="utf-8") as fp:
            json.dump(ItemAdapter(item).asdict(), fp, indent=4)

        return item
    
    def get_safe_filename(self, url):
        parsed_url = urlparse(url)
        path = parsed_url.path.strip("/").replace("/", "_")
        domain = parsed_url.netloc.replace(".", "_")
        return f"{domain}{"_" + path if path else ""}"

class PostgreSQLPipeline:
    def __init__(self, pg_host, pg_port, pg_db, pg_user, pg_password):
        self.pg_host = pg_host
        self.pg_port = pg_port
        self.pg_db = pg_db
        self.pg_user = pg_user
        self.pg_password = pg_password
        self.connection = None
        self.cursor = None
        self.product_cache = {}  # Cache to avoid duplicate product insertions

    @classmethod
    def from_crawler(cls, crawler):
        pg_host = crawler.settings.get("POSTGRES_HOST")
        pg_port = crawler.settings.get("POSTGRES_PORT")
        pg_db = crawler.settings.get("POSTGRES_DB")
        pg_user = crawler.settings.get("POSTGRES_USER")
        pg_password = crawler.settings.get("POSTGRES_PASSWORD")

        if not all([pg_host, pg_db, pg_user, pg_password]):
            logging.error("PostgreSQL connection details are not properly set. PostgreSQLPipeline will not be used.")
            raise ValueError("PostgreSQL connection details are not properly set.")

        return cls(pg_host, pg_port, pg_db, pg_user, pg_password)

    def open_spider(self, spider):
        try:
            self.connection = psycopg2.connect(
                host=self.pg_host,
                port=self.pg_port,
                dbname=self.pg_db,
                user=self.pg_user,
                password=self.pg_password
            )
            self.cursor = self.connection.cursor()
            logging.info(f"Connected to PostgreSQL database for {spider.name}.")
        except Exception as e:
            logging.error(f"Failed to connect to PostgreSQL: {e}")
            raise

    def close_spider(self, spider):
        if self.connection:
            if self.cursor:
                self.cursor.close()
            self.connection.close()
            logging.info(f"PostgreSQL connection closed for {spider.name}.")

    def process_item(self, item, spider):
        adapter = ItemAdapter(item)
        try:
            country_code = adapter.get('country_code', 'US')
            country_name = adapter.get('country_name', 'United States')
            subscriptions = adapter.get('subscriptions', [])

            for subscription_data in subscriptions:
                product_name = subscription_data.get('subscription')
                if not product_name:
                    continue

                # Get or create product
                product_id = self._get_or_create_product(product_name)

                # Process pricing for different subscription terms
                terms = subscription_data.get('terms', {})
                for term_type, price_data in terms.items():
                    if not price_data:
                        continue
                    
                    # Get the pre-extracted price value and currency
                    price_value = price_data.get('price_value')
                    currency = price_data.get('currency')
                    
                    if price_value is None:
                        continue
                    
                    # Map term types to subscription_type enum values
                    subscription_type = self._map_term_to_subscription_type(term_type)
                    
                    # Insert pricing data
                    self._insert_pricing(
                        product_id=product_id,
                        subscription_type=subscription_type,
                        price_per_month=price_value,
                        country=country_code,
                        currency=currency or 'USD'  # Default to USD if currency is None
                    )
            
            self.connection.commit()
            
        except Exception as e:
            self.connection.rollback()
            logging.error(f"Error processing item in PostgreSQLPipeline: {str(e)}")
        
        return item

    def _get_or_create_product(self, product_name):
        """Get product ID or create a new product if it doesn't exist"""
        # Check cache first
        if product_name in self.product_cache:
            return self.product_cache[product_name]
        
        try:
            # Check if product exists
            self.cursor.execute(
                "SELECT id FROM public.products WHERE name = %s",
                (product_name,)
            )
            result = self.cursor.fetchone()
            
            if result:
                product_id = result[0]
            else:
                # Create new product
                self.cursor.execute(
                    "INSERT INTO public.products (name) VALUES (%s) RETURNING id",
                    (product_name,)
                )
                product_id = self.cursor.fetchone()[0]
                self.connection.commit()
            
            # Cache the product ID
            self.product_cache[product_name] = product_id
            return product_id
            
        except Exception as e:
            self.connection.rollback()
            logging.error(f"Error in _get_or_create_product: {str(e)}")
            raise

    def _map_term_to_subscription_type(self, term):
        """Map term string to subscription_type enum value"""
        term_mapping = {
            'Monthly': 'monthly',
            '1-year': 'yearly',
            '3-year': '3 years'
        }
        return term_mapping.get(term, 'monthly')  # Default to monthly if not found

    def _insert_pricing(self, product_id, subscription_type, price_per_month, country, currency):
        """Insert pricing data into the pricing table"""
        try:
            self.cursor.execute(
                """
                INSERT INTO public.pricing 
                (product_id, subscription, price_per_month, country, currency)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (product_id, subscription_type, price_per_month, country, currency)
            )
        except Exception as e:
            logging.error(f"Error inserting pricing data: {str(e)}")
            raise