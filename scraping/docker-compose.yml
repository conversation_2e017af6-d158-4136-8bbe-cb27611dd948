services:
  mongo:
    image: mongo:latest
    container_name: graphibot-mongo
    ports:
      - "127.0.0.1:27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=yourpass
    volumes:
      - mongo_data:/data/db # This is a named volume to persist data
      # - path_on_your_host:/data/db # This is a bind mount connected to your host

volumes:
  mongo_data:
